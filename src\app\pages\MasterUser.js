// @ts-nocheck
import React, { useState, useRef, useEffect, useCallback } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, AUDIT_ROLE_URL, GROUP_EHS_ROLE_URL, REPORT_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID, GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, INDIVIDUAL_USER_LOCATION_ROLE_URL, USER_LOCATION_ROLE_WITH_ID_URL, GET_MY_USER_LOCATION_ROLE_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL, OTHER_ROLE_URL } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { userColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import FilterLocation from './FilterLocation';
import AllFilterLocation from './AllFilterLocation';
import { useSelector } from 'react-redux'
import MasterUserFilterLocation from './MasterUserFilterLocation';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const MasterUser = () => {
    const defaultMaterialTheme = createTheme();

    const [mdShow, setMdShow] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    const [allLocationRoles, setAllLocationRoles] = useState([])
    const [country, setCountry] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    useEffect(() => {
        getCountry();
        getLocationTwo();
        getLocationThree();
        getLocationFour();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();
        getGroupEhsRole();
        getReportRole();


    }, [])
    const getUserLocationRole = async (id) => {
        const response = await API.get(GET_MY_USER_LOCATION_ROLE_URL(id))

        if (response.status === 200) {

            setAllLocationRoles(response.data)
        }
    }


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, country: response.data } })
            setCountry(response.data)
        }
    }

    const getLocationTwo = async () => {
        const response = await API.get(LOCATION2_URL)

        if (response.status === 200) {

            setLocationTwo(response.data)
        }
    }

    const getLocationThree = async () => {
        const response = await API.get(LOCATION3_URL)

        if (response.status === 200) {

            setLocationThree(response.data)
        }
    }

    const getLocationFour = async () => {
        const response = await API.get(LOCATION4_URL)

        if (response.status === 200) {

            setLocationFour(response.data)
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }

    const getGroupEhsRole = async () => {
        const response = await API.get(GROUP_EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, groupEhs: response.data } })
        }
    }

    const getReportRole = async () => {
        const response = await API.get(REPORT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, report: response.data } })
        }
    }
    const uRole = useRef();

    const thead = [
        'Name',
        'Email',
        'Organization',
        'Role Assignment',

    ];

    const me = useSelector(state => state.login.user);
    const roleCountryMap = {
        "India (IN)": "India",
        "UK (UK)": "UK",
        "Singapore (SG)": "Singapore",
        "Thailand (TH)": "Thailand",
        "Korea (KR)": "Korea",
        "Philippines (PH)": "Philippines"
    };
    const roles = me.validationRoles;

    const allowedCountries = roles
        .map(role => roleCountryMap[role.name])
        .filter(country => country !== undefined);

    console.log(allowedCountries, 'allowed')

    const [data, setData] = useState([])
    useEffect(() => {
        getUsersData();
    }, [])

    const getUsersData = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            
            setData(response.data.filter(i => i.status !== false).filter(i => allowedCountries.includes(i.country)).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())));

        }
    }

    const viewAssignPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            getUserLocationRole(id)
            setMdShow(true)

        }
    }



    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)
        setIndividualSelectedRole((prevRoles) => {
            if (e.target.checked) {
                // Add the role to the selected roles
                return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
            } else {
                // Remove the role from the selected roles
                return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
            }
        });
        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category] || [];

            console.log(category, prevRoles, categoryRoles, 'check')
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        let flag = false;
        const response = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: id, roles: individualSelectedRole.roles, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } })
        if (response.status === 200) {
            flag = true;
        }
        const response2 = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response2.status === 204 && flag) {
            // setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            // setSelectedUserId({ id: "", email: "", name: "" })
            // setMdShow(false)
            // setIndividualSelectedRole({ roles: [] })
            cogoToast.info('Assigned', { position: 'top-right' })

        }

    }

    const handleAssignClose = () => {
        setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
        setSelectedUserId({ id: "", email: "", name: "" })
        setMdShow(false)
        setIndividualSelectedRole({ roles: [], disabledRoles: [] })

    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            password: uPassword.current.value,


        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            $('#dataTable').DataTable().ajax.reload();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'grading',
            tooltip: 'Role Assignment',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'Role Assignment'
        }
    };

    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

        setSelectedLocationOne(locationOneId)
        setSelectedLocationTwo(locationTwoId)
        setSelectedLocationThree(locationThreeId)
        setSelectedLocationFour(locationFourId)
    };

    const [individualSelectedRole, setIndividualSelectedRole] = useState({ roles: [], disabledRoles: [] })

    useEffect(() => {
        if (selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour)
            getIndividualRoles()
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])

    const getIndividualRoles = async () => {
        const response = await API.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: selectedUserId.id, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } });
        if (response.status === 200) {
            if (response.data && response.data.length > 0)
                setIndividualSelectedRole(response.data[0])
            else
                setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }

    const resetUserAssignment = async (id) => {
        const response = await API.delete(USER_LOCATION_ROLE_WITH_ID_URL(id));
        if (response.status === 204) {
            cogoToast.info('User Assignment Reset Success!', { position: 'top-right' })
            setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }

    const getRoleNameById = (roleId) => {
        // Search each category in allRoles for the roleId
        let roleName = 'Unknown Role'; // Default if not found
        Object.values(allRoles).forEach(category => {
            const role = category.find(role => role.id === roleId);
            if (role) {
                roleName = role.name;
                return;
            }
        });
        return roleName;
    };
    return (
        <CardOverlay>
            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={data}
                    title="Master User Data"
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions}
                    localization={localization}
                />
            </ThemeProvider>
            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
                className='extra-large'
                backdrop="static" // Prevents closing on outside click
                keyboard={false} // Prevents closing on Escape key press
            >
                <Modal.Header>
                    <div className='w-100 d-flex align-items-center justify-content-between'>
                        <h4 className='mb-0'>
                            Assign Permissions to {selectedUserId.name}
                        </h4>
                        <Button variant="primary" onClick={(e) => resetUserAssignment(selectedUserId.id)}>Reset Assignment</Button>
                    </div>

                </Modal.Header>

                <Modal.Body>

                    <div className='row'>
                        <div className='col-sm-6'>
                            <form className="forms">

                                {/* <h4>Country Admin</h4>
                                <div className='form-group mb-2'>

                                    {
                                        allRoles.country.map((i, k) => {
                                            return (
                                                <label className='me-3' key={k}>
                                                    <input value={i.id} checked={selectedRoles.country.includes(i.id)} onChange={(e) => handleRoleChange(e, 'country')} type='checkbox' /> {i.name}
                                                </label>
                                            )
                                        })
                                    }

                                </div> */}
                                <MasterUserFilterLocation handleFilter={handleFilter} countryRoles={me?.validationRoles} disableAll={true} period={false} />
                                {((selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && individualSelectedRole.roles) && <>

                                    

                                    <h4>EHS Observation</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.ehs.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                                .sort((a, b) => {
                                                    // Check if 'View Only' is in the name, and sort accordingly.
                                                    const nameA = a.name.toLowerCase();
                                                    const nameB = b.name.toLowerCase();
                                                    if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                        return -1; // 'a' should come before 'b'.
                                                    }
                                                    if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                        return 1; // 'a' should come after 'b'.
                                                    }
                                                    return 0; // No change in order.
                                                }).map((i, k) => {
                                                    return (
                                                        <label className='me-3' key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                            <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'ehs')} type='checkbox' /> {i.name}
                                                        </label>
                                                    )
                                                })
                                        }

                                    </div>
                                    <h4>ePermit to Work</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.eptw.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                                .sort((a, b) => {
                                                    // Check if 'View Only' is in the name, and sort accordingly.
                                                    const nameA = a.name.toLowerCase();
                                                    const nameB = b.name.toLowerCase();
                                                    if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                        return -1; // 'a' should come before 'b'.
                                                    }
                                                    if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                        return 1; // 'a' should come after 'b'.
                                                    }
                                                    return 0; // No change in order.
                                                }).map((i, k) => {
                                                    return (
                                                        <label className='me-3' key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                            <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'eptw')} type='checkbox' /> {i.name}
                                                        </label>
                                                    )
                                                })
                                        }

                                    </div>
                                    <h4>Incident Reporting</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.incident.slice() // Create a shallow copy of the array to avoid mutating the original array.
                                                .sort((a, b) => {
                                                    // Check if 'View Only' is in the name, and sort accordingly.
                                                    const nameA = a.name.toLowerCase();
                                                    const nameB = b.name.toLowerCase();
                                                    if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                        return -1; // 'a' should come before 'b'.
                                                    }
                                                    if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                        return 1; // 'a' should come after 'b'.
                                                    }
                                                    return 0; // No change in order.
                                                }).map((i, k) => {
                                                    return (
                                                        <label className='me-3' key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                            <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'incident')} type='checkbox' /> {i.name}
                                                        </label>
                                                    )
                                                })
                                        }

                                    </div>
                                    <h4>Inspection and Audit</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.inspection.map((i, k) => {
                                                return (
                                                    <label className='me-3' key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                        <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'inspection')} type='checkbox' /> {i.name}
                                                    </label>
                                                )
                                            })
                                        }

                                    </div>
                                    <h4>Plant and Equipment</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.plant.map((i, k) => {
                                                return (
                                                    <label className='me-3' key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                        <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'plant')} type='checkbox' /> {i.name}
                                                    </label>
                                                )
                                            })
                                        }

                                    </div>

                                    <h4>EHS Statistics</h4>
                                    <div className='form-group mb-2'>

                                        {
                                            allRoles.report.map((i, k) => {
                                                return (
                                                    <label key={k} className={`me-3 ${individualSelectedRole.disabledRoles.includes(i.id) ? 'opacity-med' : ''}`}>
                                                        <input value={i.id} checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)} disabled={individualSelectedRole.disabledRoles.includes(i.id)} onChange={(e) => handleRoleChange(e, 'report')} type='checkbox' /> {i.name}
                                                    </label>
                                                )
                                            })
                                        }

                                    </div>

                                </>}
                            </form>
                        </div>
                        <div className='col-sm-6'>
                            <div className='h-100p'>
                                <h4>List of Assigned Roles</h4>
                                <hr />
                                {allLocationRoles.map((locationRole, index) => {
                                    // Initialize an array to hold the names for each level
                                    let locationNames = [];
                                    const roleNames = locationRole.roles.map(roleId => getRoleNameById(roleId));

                                    // Function to get name by ID or handle special 'all' cases
                                    const getLocationName = (id, locationArray, allText) => {
                                        if (id === "") return ""; // Return empty if ID is not set
                                        if (id.endsWith("-all")) return allText; // Handle 'all' cases
                                        const location = locationArray.find(location => location.id === id);
                                        return location ? location.name : 'Unknown';
                                    };

                                    // Get names for each level
                                    locationNames.push(getLocationName(locationRole.locationOneId, country, 'All Country'));
                                    locationNames.push(getLocationName(locationRole.locationTwoId, locationTwo, 'All City'));
                                    locationNames.push(getLocationName(locationRole.locationThreeId, locationThree, 'All Business Unit'));
                                    locationNames.push(getLocationName(locationRole.locationFourId, locationFour, 'All Projects / DC'));

                                    // Filter out empty or unknown locations before joining
                                    locationNames = locationNames.filter(name => name && name !== 'Unknown');

                                    return (
                                        <React.Fragment key={index}>
                                            <div>
                                                {locationNames.join(' > ')} {/* Join names with ' > ' for breadcrumb style display */}
                                                <ul>
                                                    {roleNames.map((name, roleIndex) => (
                                                        <li key={roleIndex}>{name}</li> // Render each role name in a bullet point
                                                    ))}
                                                </ul>
                                            </div>
                                            <hr />
                                        </React.Fragment>
                                    );
                                })}
                            </div>

                        </div>
                    </div>

                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={handleAssignClose}>Close</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleAssignSubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>


            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_description" >Temporary Password</label>
                            <Form.Control type="password" ref={uPassword} id="user_description" placeholder="Enter Password" />
                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default MasterUser;
